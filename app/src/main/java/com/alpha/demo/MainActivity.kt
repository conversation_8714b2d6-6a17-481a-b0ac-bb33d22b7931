// MainActivity.kt
package com.alpha.demo

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.animation.AnimatedVisibilityScope
import androidx.compose.animation.EnterExitState
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.tween
import androidx.compose.animation.core.updateTransition
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.ScaleFactor
import androidx.compose.ui.layout.layoutId
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.navigation.NavHostController
import androidx.navigation.compose.*
import coil.compose.rememberAsyncImagePainter
import coil.request.ImageRequest
import kotlinx.coroutines.Dispatchers
import androidx.compose.animation.ExperimentalSharedTransitionApi
import androidx.compose.animation.SharedTransitionLayout
import androidx.compose.animation.SharedTransitionScope
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.rememberSharedContentState
import androidx.compose.animation.sharedElement
import androidx.compose.animation.sharedBounds
import androidx.compose.material3.rememberTopAppBarState
import androidx.compose.ui.layout.ContentScale.Companion.Crop
import androidx.compose.ui.layout.ContentScale.Companion.Fit
import kotlin.math.abs

class MainActivity : ComponentActivity() {
    @OptIn(ExperimentalSharedTransitionApi::class)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            MaterialTheme {
                SharedTransitionLayout {
                    val nav = rememberNavController()
                    AppNav(nav, sharedTransitionScope = this@SharedTransitionLayout)
                }
            }
        }
    }
}

// ---------- 导航 ----------
@OptIn(ExperimentalSharedTransitionApi::class)
@Composable
fun AppNav(
    nav: NavHostController,
    sharedTransitionScope: SharedTransitionScope
) {
    NavHost(navController = nav, startDestination = "grid") {
        composable("grid") {
            GridScreen(
                sharedTransitionScope = sharedTransitionScope,
                animatedVisibilityScope = this@composable,
            ) { id -> nav.navigate("detail/$id") }
        }
        composable("detail/{id}") { backStackEntry ->
            val id = backStackEntry.arguments?.getString("id")!!
            DetailScreen(
                id = id,
                sharedTransitionScope = sharedTransitionScope,
                animatedVisibilityScope = this@composable
            ) { nav.popBackStack() }
        }
    }
}

// ---------- 假数据 ----------
data class Photo(val id: String, val url: String, val aspect: Float) // aspect = w/h

private val demoPhotos = listOf(
    Photo("1", "https://images.unsplash.com/photo-1519681393784-d120267933ba", 16f / 9f),
    Photo("2", "https://images.unsplash.com/photo-1501769214405-5e86fbf4e8d2", 3f / 4f),
    Photo("3", "https://images.unsplash.com/photo-1529625054005-8c62b8b1e38f", 4f / 3f),
    Photo("4", "https://images.unsplash.com/photo-1520975916090-3105956f67fa", 1.91f / 1f),
    Photo("5", "https://images.unsplash.com/photo-1513151233558-d860c5398176", 2f / 3f),
    Photo("6", "https://images.unsplash.com/photo-1500530855697-b586d89ba3ee", 16f / 10f),
)

// ---------- 方形网格页 ----------
@OptIn(ExperimentalSharedTransitionApi::class)
@Composable
fun GridScreen(
    sharedTransitionScope: SharedTransitionScope,
    animatedVisibilityScope: AnimatedVisibilityScope,
    onClick: (String) -> Unit
) {
    Scaffold(topBar = { TopAppBar(title = { Text("Gallery") }) }) { padding ->
        LazyVerticalGrid(
            columns = GridCells.Fixed(3),
            contentPadding = padding,
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            modifier = Modifier.fillMaxSize().padding(8.dp)
        ) {
            items(demoPhotos) { p ->
                PhotoSquareCell(
                    p, onClick,
                    sharedTransitionScope = sharedTransitionScope,
                    animatedVisibilityScope = animatedVisibilityScope
                )
            }
        }
    }
}

@OptIn(ExperimentalSharedTransitionApi::class)
@Composable
private fun PhotoSquareCell(
    photo: Photo,
    onClick: (String) -> Unit,
    sharedTransitionScope: SharedTransitionScope,
    animatedVisibilityScope: AnimatedVisibilityScope
) {
    val painter = rememberImagePainter(photo.url)
    with(sharedTransitionScope) {
        // 外层容器也跟随尺寸做 shared bounds（可选但观感更丝滑）
        Box(
            modifier = Modifier
                .sharedBounds(
                    rememberSharedContentState("card-${photo.id}"),
                    animatedVisibilityScope = animatedVisibilityScope
                )
                .aspectRatio(1f)
                .clip(RoundedCornerShape(16.dp))
                .clickable { onClick(photo.id) }
        ) {
            Image(
                painter = painter,
                contentDescription = null,
                modifier = Modifier
                    .sharedElement(
                        rememberSharedContentState("photo-${photo.id}"),
                        animatedVisibilityScope = animatedVisibilityScope
                    )
                    .fillMaxSize()
                    .clip(RoundedCornerShape(16.dp)), // 圆角参与共享
                contentScale = Crop // 网格使用裁剪成正方形
            )
        }
    }
}

// ---------- 详情页（长图） ----------
@OptIn(ExperimentalSharedTransitionApi::class)
@Composable
fun DetailScreen(
    id: String,
    sharedTransitionScope: SharedTransitionScope,
    animatedVisibilityScope: AnimatedVisibilityScope,
    onBack: () -> Unit
) {
    val photo = remember(id) { demoPhotos.first { it.id == id } }
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Photo #$id", maxLines = 1, overflow = TextOverflow.Ellipsis) },
                navigationIcon = {
                    IconButton(onClick = onBack) {
                        Icon(androidx.compose.material.icons.Icons.Default.ArrowBack, contentDescription = "Back")
                    }
                }
            )
        }
    ) { padding ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(padding),
            contentAlignment = Alignment.TopCenter
        ) {
            val painter = rememberImagePainter(photo.url)

            // 使用 AnimatedVisibilityScope 的 transition 作为“统一时间轴”
            val transition = animatedVisibilityScope.transition
            // 进度：0f(开始/不可见) -> 1f(完全可见)
            val progress by transition.animateFloat(label = "cropProgress") { state ->
                if (state == EnterExitState.Visible) 1f else 0f
            }

            with(sharedTransitionScope) {
                // 外层容器尺寸从方 → 原图长宽比
                Box(
                    modifier = Modifier
                        .sharedBounds(
                            rememberSharedContentState("card-${photo.id}"),
                            animatedVisibilityScope = animatedVisibilityScope,
                            // 调整补间曲线/时长（可选）
                            boundsTransform = { _, _ ->
                                tween(durationMillis = 520, easing = FastOutSlowInEasing)
                            }
                        )
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp, vertical = 12.dp)
                        .aspectRatio(photo.aspect)
                        .clip(RoundedCornerShape(8.dp))
                ) {
                    Image(
                        painter = painter,
                        contentDescription = null,
                        modifier = Modifier
                            .sharedElement(
                                rememberSharedContentState("photo-${photo.id}"),
                                animatedVisibilityScope = animatedVisibilityScope,
                                boundsTransform = { _, _ ->
                                    tween(durationMillis = 520, easing = FastOutSlowInEasing)
                                }
                            )
                            .fillMaxSize()
                            .clip(RoundedCornerShape(8.dp)),
                        // ☆ 核心：裁剪从 Crop → Fit 的“可插值 ContentScale”
                        contentScale = interpolatingContentScale(
                            from = Crop,
                            to = Fit,
                            progress = progress
                        )
                    )
                }
            }
        }
    }
}

// ---------- 可插值 ContentScale：在 0..1 间插值 ScaleFactor ----------
/**
 * 在 [from] 与 [to] 两种 ContentScale 之间做线性插值。
 * 仅依赖 progress(0..1)，无需你手动计算尺寸；Image 在测量时会传入 src/dst 尺寸。
 */
fun interpolatingContentScale(
    from: ContentScale,
    to: ContentScale,
    progress: Float
): ContentScale = object : ContentScale {
    override fun computeScaleFactor(srcSize: androidx.compose.ui.geometry.Size,
                                    dstSize: androidx.compose.ui.geometry.Size): ScaleFactor {
        val a = from.computeScaleFactor(srcSize, dstSize)
        val b = to.computeScaleFactor(srcSize, dstSize)
        return lerp(a, b, progress.coerceIn(0f, 1f))
    }
}

// 线性插值 ScaleFactor（Compose 暂未提供现成 lerp，可自写）
private fun lerp(a: ScaleFactor, b: ScaleFactor, t: Float): ScaleFactor {
    val p = t.coerceIn(0f, 1f)
    val sx = a.scaleX + (b.scaleX - a.scaleX) * p
    val sy = a.scaleY + (b.scaleY - a.scaleY) * p
    return ScaleFactor(sx, sy)
}

// ---------- Coil 工具 ----------
@Composable
private fun rememberImagePainter(url: String) = rememberAsyncImagePainter(
    ImageRequest.Builder(LocalContext.current)
        .data(url)
        .dispatcher(Dispatchers.IO)
        .crossfade(true)
        .build()
)
